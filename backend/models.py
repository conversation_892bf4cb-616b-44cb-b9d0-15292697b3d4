#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据库模型定义
版本: 1.0
创建时间: 2025-01-13

定义所有数据库表结构和模型关系
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import Numeric
import json

# 数据库实例
db = SQLAlchemy()

class User(db.Model):
    """
    用户模型
    包含用户基本信息、认证信息和权限管理
    """
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True, comment='用户ID')
    username = db.Column(db.String(50), unique=True, nullable=False, comment='用户名')
    email = db.Column(db.String(100), unique=True, nullable=False, comment='邮箱')
    password_hash = db.Column(db.String(255), nullable=False, comment='密码哈希')
    user_type = db.Column(db.Enum('airline', 'supplier', 'maintenance', 'admin', name='user_types'), 
                         nullable=False, comment='用户类型')
    company_name = db.Column(db.String(100), comment='公司名称')
    real_name = db.Column(db.String(50), comment='真实姓名')
    phone = db.Column(db.String(20), comment='联系电话')
    status = db.Column(db.Enum('active', 'inactive', 'suspended', name='user_status'), 
                      default='active', comment='用户状态')
    last_login = db.Column(db.DateTime, comment='最后登录时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    orders_as_buyer = db.relationship('Order', foreign_keys='Order.buyer_id', backref='buyer', lazy='dynamic')
    orders_as_supplier = db.relationship('Order', foreign_keys='Order.supplier_id', backref='supplier', lazy='dynamic')
    demands = db.relationship('Demand', backref='requester', lazy='dynamic')
    notifications = db.relationship('Notification', backref='user', lazy='dynamic')
    supplied_inventory = db.relationship('InventoryItem', backref='inventory_supplier', lazy='dynamic')
    
    def set_password(self, password):
        """设置密码哈希"""
        self.password_hash = generate_password_hash(password, method='pbkdf2:sha256')
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'user_type': self.user_type,
            'company_name': self.company_name,
            'real_name': self.real_name,
            'phone': self.phone,
            'status': self.status,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat()
        }

class Material(db.Model):
    """
    航材模型
    存储航材基本信息和规格参数
    """
    __tablename__ = 'materials'
    
    id = db.Column(db.Integer, primary_key=True, comment='航材ID')
    part_number = db.Column(db.String(50), unique=True, nullable=False, comment='零件号')
    part_name = db.Column(db.String(200), nullable=False, comment='零件名称')
    category = db.Column(db.String(50), nullable=False, comment='类别')
    manufacturer = db.Column(db.String(100), comment='制造商')
    aircraft_type = db.Column(db.String(50), comment='适用机型')
    description = db.Column(db.Text, comment='描述')
    specifications = db.Column(db.Text, comment='规格参数(JSON)')
    unit = db.Column(db.String(20), default='PCS', comment='计量单位')
    weight = db.Column(db.Float, comment='重量(kg)')
    shelf_life_months = db.Column(db.Integer, comment='保质期(月)')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    inventory_items = db.relationship('InventoryItem', backref='material', lazy='dynamic')
    order_items = db.relationship('OrderItem', backref='material', lazy='dynamic')
    certificates = db.relationship('Certificate', backref='material', lazy='dynamic')
    
    def get_specifications(self):
        """获取规格参数"""
        return json.loads(self.specifications) if self.specifications else {}
    
    def set_specifications(self, specs_dict):
        """设置规格参数"""
        self.specifications = json.dumps(specs_dict, ensure_ascii=False)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'part_number': self.part_number,
            'part_name': self.part_name,
            'category': self.category,
            'manufacturer': self.manufacturer,
            'aircraft_type': self.aircraft_type,
            'description': self.description,
            'specifications': self.get_specifications(),
            'unit': self.unit,
            'weight': self.weight,
            'shelf_life_months': self.shelf_life_months,
            'created_at': self.created_at.isoformat()
        }

class InventoryItem(db.Model):
    """
    库存项目模型
    管理航材库存数量和状态
    """
    __tablename__ = 'inventory_items'
    
    id = db.Column(db.Integer, primary_key=True, comment='库存ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='航材ID')
    location = db.Column(db.String(100), nullable=False, comment='存储位置')
    current_stock = db.Column(db.Integer, default=0, comment='当前库存')
    safety_stock = db.Column(db.Integer, default=0, comment='安全库存')
    unit_price = db.Column(Numeric(12, 2), comment='单价')
    condition_code = db.Column(db.Enum('NE', 'NS', 'OH', 'SV', 'AR'), default='NE', comment='状况代码')
    is_shareable = db.Column(db.Boolean, default=False, comment='是否可共享')
    status = db.Column(db.Enum('normal', 'warning', 'shortage', 'expired', name='inventory_status'), 
                      default='normal', comment='库存状态')
    batch_number = db.Column(db.String(50), comment='批次号')
    expiry_date = db.Column(db.Date, comment='过期日期')
    supplier_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='供应商ID')
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='最后更新时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    supplier = db.relationship('User', backref='managed_inventory')
    
    def update_status(self):
        """更新库存状态"""
        if self.current_stock <= 0:
            self.status = 'shortage'
        elif self.current_stock <= self.safety_stock:
            self.status = 'warning'
        elif self.expiry_date and self.expiry_date < datetime.now().date():
            self.status = 'expired'
        else:
            self.status = 'normal'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'material_id': self.material_id,
            'material': self.material.to_dict() if self.material else None,
            'location': self.location,
            'current_stock': self.current_stock,
            'safety_stock': self.safety_stock,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'condition_code': self.condition_code,
            'is_shareable': self.is_shareable,
            'status': self.status,
            'batch_number': self.batch_number,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'supplier_id': self.supplier_id,
            'supplier': self.supplier.to_dict() if self.supplier else None,
            'last_updated': self.last_updated.isoformat(),
            'created_at': self.created_at.isoformat()
        }

class Order(db.Model):
    """
    订单模型
    管理采购订单和交易信息
    """
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True, comment='订单ID')
    order_number = db.Column(db.String(50), unique=True, nullable=False, comment='订单号')
    buyer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='买方ID')
    supplier_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='供应商ID')
    status = db.Column(db.Enum('pending', 'confirmed', 'processing', 'shipping', 'completed', 'cancelled', 
                               name='order_status'), default='pending', comment='订单状态')
    priority = db.Column(db.Enum('aog', 'high', 'normal', 'low', name='order_priority'), 
                        default='normal', comment='优先级')
    total_amount = db.Column(Numeric(12, 2), comment='订单总金额')
    currency = db.Column(db.String(3), default='CNY', comment='货币类型')
    delivery_address = db.Column(db.Text, comment='交货地址')
    delivery_date = db.Column(db.DateTime, comment='交货日期')
    notes = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    order_items = db.relationship('OrderItem', backref='order', lazy='dynamic', cascade='all, delete-orphan')
    logistics = db.relationship('LogisticsInfo', backref='order', uselist=False)
    
    def calculate_total(self):
        """计算订单总金额"""
        total = sum(item.subtotal for item in self.order_items)
        self.total_amount = total
        return total
    
    def update_status(self, new_status, operator_id, reason=None, notes=None):
        """更新订单状态并记录历史"""
        from models import OrderStatusHistory  # 避免循环导入

        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.utcnow()

        # 记录状态变更历史
        history = OrderStatusHistory(
            order_id=self.id,
            from_status=old_status,
            to_status=new_status,
            operator_id=operator_id,
            reason=reason,
            notes=notes
        )
        db.session.add(history)

        return history

    def can_transition_to(self, new_status):
        """检查是否可以转换到新状态"""
        valid_transitions = {
            'pending': ['confirmed', 'cancelled'],
            'confirmed': ['processing', 'cancelled'],
            'processing': ['shipping', 'cancelled'],
            'shipping': ['completed'],
            'completed': [],
            'cancelled': []
        }

        return new_status in valid_transitions.get(self.status, [])

    def requires_approval(self):
        """检查订单是否需要审批"""
        # AOG订单或高金额订单需要审批
        if self.priority == 'aog':
            return True
        if self.total_amount and self.total_amount > 100000:  # 10万以上需要审批
            return True
        return False

    def get_approval_status(self):
        """获取审批状态"""
        if not self.requires_approval():
            return 'not_required'

        approvals = self.approvals
        if not approvals:
            return 'pending'

        # 检查是否有拒绝的审批
        for approval in approvals:
            if approval.status == 'rejected':
                return 'rejected'

        # 检查是否所有审批都通过
        required_levels = self._get_required_approval_levels()
        approved_levels = [a.approval_level for a in approvals if a.status == 'approved']

        if all(level in approved_levels for level in required_levels):
            return 'approved'

        return 'pending'

    def _get_required_approval_levels(self):
        """获取需要的审批级别"""
        levels = []

        if self.priority == 'aog':
            levels.append(1)  # 部门主管

        if self.total_amount:
            if self.total_amount > 100000:
                levels.append(1)  # 部门主管
            if self.total_amount > 500000:
                levels.append(2)  # 财务主管
            if self.total_amount > 1000000:
                levels.append(3)  # 总经理

        return levels or [1]  # 默认需要一级审批

    def to_dict(self, include_items=True, include_history=False, include_approvals=False):
        """转换为字典格式"""
        result = {
            'id': self.id,
            'order_number': self.order_number,
            'buyer_id': self.buyer_id,
            'supplier_id': self.supplier_id,
            'status': self.status,
            'priority': self.priority,
            'total_amount': float(self.total_amount) if self.total_amount else None,
            'currency': self.currency,
            'delivery_address': self.delivery_address,
            'delivery_date': self.delivery_date.isoformat() if self.delivery_date else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'requires_approval': self.requires_approval(),
            'approval_status': self.get_approval_status()
        }

        if include_items:
            result['items'] = [item.to_dict() for item in self.order_items]

        if include_history:
            result['status_history'] = [h.to_dict() for h in self.status_history]

        if include_approvals:
            result['approvals'] = [a.to_dict() for a in self.approvals]

        return result

class OrderItem(db.Model):
    """
    订单项目模型
    订单中的具体航材项目
    """
    __tablename__ = 'order_items'
    
    id = db.Column(db.Integer, primary_key=True, comment='订单项ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='航材ID')
    quantity = db.Column(db.Integer, nullable=False, comment='数量')
    unit_price = db.Column(Numeric(12, 2), comment='单价')
    subtotal = db.Column(Numeric(12, 2), comment='小计')
    condition_code = db.Column(db.String(10), comment='状况代码')
    delivery_requirement = db.Column(db.Text, comment='交货要求')
    
    def calculate_subtotal(self):
        """计算小计"""
        if self.quantity and self.unit_price:
            self.subtotal = self.quantity * self.unit_price
        return self.subtotal
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'material_id': self.material_id,
            'material': self.material.to_dict() if self.material else None,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'subtotal': float(self.subtotal) if self.subtotal else None,
            'condition_code': self.condition_code,
            'delivery_requirement': self.delivery_requirement
        }

class OrderStatusHistory(db.Model):
    """
    订单状态历史模型
    记录订单状态变更的历史记录
    """
    __tablename__ = 'order_status_history'

    id = db.Column(db.Integer, primary_key=True, comment='历史记录ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    from_status = db.Column(db.String(20), comment='原状态')
    to_status = db.Column(db.String(20), nullable=False, comment='新状态')
    operator_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='操作人ID')
    reason = db.Column(db.Text, comment='状态变更原因')
    notes = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')

    # 关系
    order = db.relationship('Order', backref='status_history')
    operator = db.relationship('User', backref='operated_order_status')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'from_status': self.from_status,
            'to_status': self.to_status,
            'operator_id': self.operator_id,
            'operator': self.operator.to_dict() if self.operator else None,
            'reason': self.reason,
            'notes': self.notes,
            'created_at': self.created_at.isoformat()
        }

class OrderApproval(db.Model):
    """
    订单审批模型
    管理订单的审批流程
    """
    __tablename__ = 'order_approvals'

    id = db.Column(db.Integer, primary_key=True, comment='审批记录ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    approver_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='审批人ID')
    approval_level = db.Column(db.Integer, nullable=False, comment='审批级别')
    status = db.Column(db.Enum('pending', 'approved', 'rejected', name='approval_status'),
                      default='pending', comment='审批状态')
    comments = db.Column(db.Text, comment='审批意见')
    approved_at = db.Column(db.DateTime, comment='审批时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')

    # 关系
    order = db.relationship('Order', backref='approvals')
    approver = db.relationship('User', backref='order_approvals')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'approver_id': self.approver_id,
            'approver': self.approver.to_dict() if self.approver else None,
            'approval_level': self.approval_level,
            'status': self.status,
            'comments': self.comments,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'created_at': self.created_at.isoformat()
        }

class OrderAttachment(db.Model):
    """
    订单附件模型
    管理订单相关的文件附件
    """
    __tablename__ = 'order_attachments'

    id = db.Column(db.Integer, primary_key=True, comment='附件ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    filename = db.Column(db.String(255), nullable=False, comment='文件名')
    original_filename = db.Column(db.String(255), nullable=False, comment='原始文件名')
    file_path = db.Column(db.String(500), nullable=False, comment='文件路径')
    file_size = db.Column(db.Integer, comment='文件大小(字节)')
    file_type = db.Column(db.String(50), comment='文件类型')
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='上传人ID')
    description = db.Column(db.Text, comment='文件描述')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='上传时间')

    # 关系
    order = db.relationship('Order', backref='attachments')
    uploader = db.relationship('User', backref='uploaded_order_attachments')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'uploaded_by': self.uploaded_by,
            'uploader': self.uploader.to_dict() if self.uploader else None,
            'description': self.description,
            'created_at': self.created_at.isoformat()
        }

class Certificate(db.Model):
    """
    适航证书模型
    管理航材的适航证书和认证信息
    """
    __tablename__ = 'certificates'
    
    id = db.Column(db.Integer, primary_key=True, comment='证书ID')
    certificate_number = db.Column(db.String(100), unique=True, nullable=False, comment='证书编号')
    material_id = db.Column(db.Integer, db.ForeignKey('materials.id'), nullable=False, comment='航材ID')
    certificate_type = db.Column(db.Enum('8130-3', 'caac', 'faa', 'easa', name='certificate_types'), 
                                nullable=False, comment='证书类型')
    issuing_authority = db.Column(db.String(100), nullable=False, comment='颁发机构')
    issue_date = db.Column(db.Date, nullable=False, comment='颁发日期')
    expiry_date = db.Column(db.Date, comment='到期日期')
    status = db.Column(db.Enum('valid', 'expiring', 'expired', 'suspended', name='certificate_status'), 
                      default='valid', comment='证书状态')
    document_path = db.Column(db.String(255), comment='证书文件路径')
    verification_status = db.Column(db.Boolean, default=False, comment='验证状态')
    notes = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def update_status(self):
        """更新证书状态"""
        if self.expiry_date:
            days_to_expiry = (self.expiry_date - datetime.now().date()).days
            if days_to_expiry < 0:
                self.status = 'expired'
            elif days_to_expiry <= 30:
                self.status = 'expiring'
            else:
                self.status = 'valid'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'certificate_number': self.certificate_number,
            'material_id': self.material_id,
            'material': self.material.to_dict() if self.material else None,
            'certificate_type': self.certificate_type,
            'issuing_authority': self.issuing_authority,
            'issue_date': self.issue_date.isoformat(),
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'status': self.status,
            'document_path': self.document_path,
            'verification_status': self.verification_status,
            'notes': self.notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class WorkOrder(db.Model):
    """
    维修工单模型
    管理维修工作订单和进度
    """
    __tablename__ = 'work_orders'
    
    id = db.Column(db.Integer, primary_key=True, comment='工单ID')
    work_order_number = db.Column(db.String(50), unique=True, nullable=False, comment='工单号')
    aircraft_tail = db.Column(db.String(20), nullable=False, comment='飞机尾号')
    aircraft_type = db.Column(db.String(50), nullable=False, comment='机型')
    priority = db.Column(db.Enum('aog', 'high', 'normal', 'low', name='work_order_priority'), 
                        default='normal', comment='优先级')
    status = db.Column(db.Enum('new', 'in_progress', 'waiting_parts', 'completed', 'cancelled', 
                               name='work_order_status'), default='new', comment='工单状态')
    fault_title = db.Column(db.String(200), nullable=False, comment='故障标题')
    fault_description = db.Column(db.Text, comment='故障描述')
    station = db.Column(db.String(100), comment='维修站')
    assigned_technician_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='分配技师ID')
    estimated_hours = db.Column(db.Float, comment='预计工时')
    actual_hours = db.Column(db.Float, comment='实际工时')
    progress = db.Column(db.Integer, default=0, comment='进度百分比')
    start_time = db.Column(db.DateTime, comment='开始时间')
    estimated_completion = db.Column(db.DateTime, comment='预计完成时间')
    completion_time = db.Column(db.DateTime, comment='实际完成时间')
    notes = db.Column(db.Text, comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    assigned_technician = db.relationship('User', backref='assigned_work_orders')
    labor_records = db.relationship('LaborRecord', backref='work_order', lazy='dynamic')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'work_order_number': self.work_order_number,
            'aircraft_tail': self.aircraft_tail,
            'aircraft_type': self.aircraft_type,
            'priority': self.priority,
            'status': self.status,
            'fault_title': self.fault_title,
            'fault_description': self.fault_description,
            'station': self.station,
            'assigned_technician_id': self.assigned_technician_id,
            'assigned_technician': self.assigned_technician.to_dict() if self.assigned_technician else None,
            'estimated_hours': self.estimated_hours,
            'actual_hours': self.actual_hours,
            'progress': self.progress,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'estimated_completion': self.estimated_completion.isoformat() if self.estimated_completion else None,
            'completion_time': self.completion_time.isoformat() if self.completion_time else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class LaborRecord(db.Model):
    """
    工时记录模型
    记录维修工作的工时信息
    """
    __tablename__ = 'labor_records'
    
    id = db.Column(db.Integer, primary_key=True, comment='工时记录ID')
    work_order_id = db.Column(db.Integer, db.ForeignKey('work_orders.id'), nullable=False, comment='工单ID')
    technician_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='技师ID')
    start_time = db.Column(db.DateTime, nullable=False, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    hours_worked = db.Column(db.Float, comment='工作小时数')
    work_description = db.Column(db.Text, comment='工作描述')
    labor_rate = db.Column(Numeric(10, 2), comment='工时费率')
    labor_cost = db.Column(Numeric(10, 2), comment='工时成本')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    # 关系
    technician = db.relationship('User', backref='labor_records')
    
    def calculate_hours(self):
        """计算工作小时数"""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            self.hours_worked = delta.total_seconds() / 3600
        return self.hours_worked
    
    def calculate_cost(self):
        """计算工时成本"""
        if self.hours_worked and self.labor_rate:
            self.labor_cost = self.hours_worked * self.labor_rate
        return self.labor_cost
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'work_order_id': self.work_order_id,
            'technician_id': self.technician_id,
            'technician': self.technician.to_dict() if self.technician else None,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'hours_worked': self.hours_worked,
            'work_description': self.work_description,
            'labor_rate': float(self.labor_rate) if self.labor_rate else None,
            'labor_cost': float(self.labor_cost) if self.labor_cost else None,
            'created_at': self.created_at.isoformat()
        }

class AOGCase(db.Model):
    """
    AOG紧急案例模型
    管理Aircraft on Ground紧急响应案例
    """
    __tablename__ = 'aog_cases'
    
    id = db.Column(db.Integer, primary_key=True, comment='AOG案例ID')
    case_number = db.Column(db.String(50), unique=True, nullable=False, comment='案例编号')
    aircraft_tail = db.Column(db.String(20), nullable=False, comment='飞机尾号')
    aircraft_type = db.Column(db.String(50), nullable=False, comment='机型')
    location = db.Column(db.String(100), nullable=False, comment='位置')
    priority = db.Column(db.Enum('critical', 'high', 'medium', name='aog_priority'), 
                        default='critical', comment='优先级')
    status = db.Column(db.Enum('new', 'responding', 'in_progress', 'waiting_parts', 'resolved', 
                               name='aog_status'), default='new', comment='状态')
    fault_title = db.Column(db.String(200), nullable=False, comment='故障标题')
    fault_description = db.Column(db.Text, comment='故障描述')
    customer_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='客户ID')
    assigned_team_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='分配团队ID')
    contact_name = db.Column(db.String(100), comment='联系人姓名')
    contact_phone = db.Column(db.String(20), comment='联系电话')
    contact_email = db.Column(db.String(100), comment='联系邮箱')
    response_time = db.Column(db.DateTime, comment='响应时间')
    estimated_resolution = db.Column(db.DateTime, comment='预计解决时间')
    resolution_time = db.Column(db.DateTime, comment='解决时间')
    resolution_notes = db.Column(db.Text, comment='解决方案备注')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    customer = db.relationship('User', foreign_keys=[customer_id], backref='aog_cases_as_customer')
    assigned_team = db.relationship('User', foreign_keys=[assigned_team_id], backref='aog_cases_as_team')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'case_number': self.case_number,
            'aircraft': {
                'tail_number': self.aircraft_tail,
                'aircraft_type': self.aircraft_type,
                'location': self.location
            },
            'priority': self.priority,
            'status': self.status,
            'fault': {
                'title': self.fault_title,
                'description': self.fault_description
            },
            'customer_id': self.customer_id,
            'assigned_team_id': self.assigned_team_id,
            'assigned_team': self.assigned_team.to_dict() if self.assigned_team else None,
            'contact': {
                'name': self.contact_name,
                'phone': self.contact_phone,
                'email': self.contact_email
            },
            'response_time': self.response_time.isoformat() if self.response_time else None,
            'estimated_resolution': self.estimated_resolution.isoformat() if self.estimated_resolution else None,
            'resolution_time': self.resolution_time.isoformat() if self.resolution_time else None,
            'resolution_notes': self.resolution_notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Demand(db.Model):
    """
    需求模型
    管理用户发布的航材需求
    """
    __tablename__ = 'demands'
    
    id = db.Column(db.Integer, primary_key=True, comment='需求ID')
    demand_number = db.Column(db.String(50), unique=True, nullable=False, comment='需求编号')
    requester_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='需求方ID')
    type = db.Column(db.Enum('turnaround', 'consumable', 'maintenance', 'aog', name='demand_types'), 
                     nullable=False, comment='需求类型')
    priority = db.Column(db.Enum('aog', 'high', 'normal', 'low', name='demand_priority'), 
                        default='normal', comment='优先级')
    status = db.Column(db.Enum('published', 'matched', 'negotiating', 'confirmed', 'cancelled', 
                               name='demand_status'), default='published', comment='状态')
    material_name = db.Column(db.String(200), nullable=False, comment='航材名称')
    part_number = db.Column(db.String(50), comment='零件号')
    aircraft_type = db.Column(db.String(50), comment='机型')
    quantity = db.Column(db.Integer, nullable=False, comment='需求数量')
    unit = db.Column(db.String(20), default='PCS', comment='单位')
    description = db.Column(db.Text, comment='需求描述')
    delivery_location = db.Column(db.String(200), comment='交货地点')
    delivery_time = db.Column(db.DateTime, comment='交货时间')
    budget_range = db.Column(db.String(100), comment='预算范围')
    quality_requirements = db.Column(db.Text, comment='质量要求')
    contact_info = db.Column(db.Text, comment='联系信息(JSON)')
    expires_at = db.Column(db.DateTime, comment='需求过期时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    responses = db.relationship('DemandResponse', backref='demand', lazy='dynamic')
    
    def get_contact_info(self):
        """获取联系信息"""
        return json.loads(self.contact_info) if self.contact_info else {}
    
    def set_contact_info(self, contact_dict):
        """设置联系信息"""
        self.contact_info = json.dumps(contact_dict, ensure_ascii=False)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'demand_number': self.demand_number,
            'requester_id': self.requester_id,
            'requester': self.requester.to_dict() if self.requester else None,
            'type': self.type,
            'priority': self.priority,
            'status': self.status,
            'material_name': self.material_name,
            'part_number': self.part_number,
            'aircraft_type': self.aircraft_type,
            'quantity': self.quantity,
            'unit': self.unit,
            'description': self.description,
            'delivery_location': self.delivery_location,
            'delivery_time': self.delivery_time.isoformat() if self.delivery_time else None,
            'budget_range': self.budget_range,
            'quality_requirements': self.quality_requirements,
            'contact_info': self.get_contact_info(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class DemandResponse(db.Model):
    """
    需求响应模型
    供应商对需求的响应
    """
    __tablename__ = 'demand_responses'
    
    id = db.Column(db.Integer, primary_key=True, comment='响应ID')
    demand_id = db.Column(db.Integer, db.ForeignKey('demands.id'), nullable=False, comment='需求ID')
    supplier_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='供应商ID')
    status = db.Column(db.Enum('pending', 'accepted', 'rejected', 'expired', name='response_status'), 
                      default='pending', comment='响应状态')
    unit_price = db.Column(Numeric(12, 2), comment='单价')
    total_price = db.Column(Numeric(12, 2), comment='总价')
    delivery_time = db.Column(db.DateTime, comment='交货时间')
    availability = db.Column(db.Integer, comment='可供应数量')
    condition_code = db.Column(db.String(10), comment='状况代码')
    certificate_info = db.Column(db.Text, comment='证书信息')
    message = db.Column(db.Text, comment='响应消息')
    valid_until = db.Column(db.DateTime, comment='报价有效期')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    supplier = db.relationship('User', backref='demand_responses')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'demand_id': self.demand_id,
            'supplier_id': self.supplier_id,
            'supplier': self.supplier.to_dict() if self.supplier else None,
            'status': self.status,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'total_price': float(self.total_price) if self.total_price else None,
            'delivery_time': self.delivery_time.isoformat() if self.delivery_time else None,
            'availability': self.availability,
            'condition_code': self.condition_code,
            'certificate_info': self.certificate_info,
            'message': self.message,
            'valid_until': self.valid_until.isoformat() if self.valid_until else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class LogisticsInfo(db.Model):
    """
    物流信息模型
    跟踪订单的物流配送信息
    """
    __tablename__ = 'logistics_info'
    
    id = db.Column(db.Integer, primary_key=True, comment='物流ID')
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    tracking_number = db.Column(db.String(100), comment='运单号')
    carrier = db.Column(db.String(100), comment='承运商')
    status = db.Column(db.Enum('preparing', 'shipped', 'in_transit', 'arrived', 'delivered', 'exception', 
                               name='logistics_status'), default='preparing', comment='物流状态')
    shipped_at = db.Column(db.DateTime, comment='发货时间')
    estimated_arrival = db.Column(db.DateTime, comment='预计到达时间')
    actual_arrival = db.Column(db.DateTime, comment='实际到达时间')
    current_location = db.Column(db.String(200), comment='当前位置')
    delivery_notes = db.Column(db.Text, comment='配送备注')
    recipient_name = db.Column(db.String(100), comment='收货人姓名')
    recipient_phone = db.Column(db.String(20), comment='收货人电话')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'tracking_number': self.tracking_number,
            'carrier': self.carrier,
            'status': self.status,
            'shipped_at': self.shipped_at.isoformat() if self.shipped_at else None,
            'estimated_arrival': self.estimated_arrival.isoformat() if self.estimated_arrival else None,
            'actual_arrival': self.actual_arrival.isoformat() if self.actual_arrival else None,
            'current_location': self.current_location,
            'delivery_notes': self.delivery_notes,
            'recipient_name': self.recipient_name,
            'recipient_phone': self.recipient_phone,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Notification(db.Model):
    """
    通知模型
    系统通知和消息管理
    """
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True, comment='通知ID')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    type = db.Column(db.Enum('order', 'aog', 'logistics', 'quality', 'supplier', 'system', 
                             name='notification_types'), nullable=False, comment='通知类型')
    title = db.Column(db.String(200), nullable=False, comment='通知标题')
    content = db.Column(db.Text, comment='通知内容')
    priority = db.Column(db.Enum('low', 'normal', 'high', 'urgent', name='notification_priority'), 
                        default='normal', comment='优先级')
    is_read = db.Column(db.Boolean, default=False, comment='是否已读')
    read_at = db.Column(db.DateTime, comment='阅读时间')
    related_id = db.Column(db.Integer, comment='关联对象ID')
    related_type = db.Column(db.String(50), comment='关联对象类型')
    expires_at = db.Column(db.DateTime, comment='过期时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    
    def mark_as_read(self):
        """标记为已读"""
        self.is_read = True
        self.read_at = datetime.utcnow()
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'type': self.type,
            'title': self.title,
            'content': self.content,
            'priority': self.priority,
            'is_read': self.is_read,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'related_id': self.related_id,
            'related_type': self.related_type,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat()
        }

class WorkflowInstance(db.Model):
    """
    工作流实例模型
    管理业务流程的审批实例
    """
    __tablename__ = 'workflow_instances'
    
    id = db.Column(db.Integer, primary_key=True, comment='工作流实例ID')
    workflow_name = db.Column(db.String(100), nullable=False, comment='工作流名称')
    business_type = db.Column(db.String(50), nullable=False, comment='业务类型')
    business_id = db.Column(db.Integer, nullable=False, comment='业务对象ID')
    initiator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='发起人ID')
    current_step = db.Column(db.String(100), comment='当前步骤')
    status = db.Column(db.Enum('running', 'completed', 'rejected', 'cancelled', name='workflow_status'), 
                      default='running', comment='状态')
    data = db.Column(db.Text, comment='流程数据(JSON)')
    started_at = db.Column(db.DateTime, default=datetime.utcnow, comment='开始时间')
    completed_at = db.Column(db.DateTime, comment='完成时间')
    
    # 关系
    initiator = db.relationship('User', backref='initiated_workflows')
    tasks = db.relationship('WorkflowTask', backref='workflow_instance', lazy='dynamic')
    
    def get_data(self):
        """获取流程数据"""
        return json.loads(self.data) if self.data else {}
    
    def set_data(self, data_dict):
        """设置流程数据"""
        self.data = json.dumps(data_dict, ensure_ascii=False)
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'workflow_name': self.workflow_name,
            'business_type': self.business_type,
            'business_id': self.business_id,
            'initiator_id': self.initiator_id,
            'initiator': self.initiator.to_dict() if self.initiator else None,
            'current_step': self.current_step,
            'status': self.status,
            'data': self.get_data(),
            'started_at': self.started_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }

class WorkflowTask(db.Model):
    """
    工作流任务模型
    管理工作流中的具体审批任务
    """
    __tablename__ = 'workflow_tasks'
    
    id = db.Column(db.Integer, primary_key=True, comment='任务ID')
    workflow_instance_id = db.Column(db.Integer, db.ForeignKey('workflow_instances.id'), 
                                   nullable=False, comment='工作流实例ID')
    step_name = db.Column(db.String(100), nullable=False, comment='步骤名称')
    assignee_id = db.Column(db.Integer, db.ForeignKey('users.id'), comment='指派人ID')
    status = db.Column(db.Enum('pending', 'approved', 'rejected', 'cancelled', name='task_status'), 
                      default='pending', comment='任务状态')
    comment = db.Column(db.Text, comment='处理意见')
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow, comment='分配时间')
    completed_at = db.Column(db.DateTime, comment='完成时间')
    
    # 关系
    assignee = db.relationship('User', backref='workflow_tasks')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'workflow_instance_id': self.workflow_instance_id,
            'step_name': self.step_name,
            'assignee_id': self.assignee_id,
            'assignee': self.assignee.to_dict() if self.assignee else None,
            'status': self.status,
            'comment': self.comment,
            'assigned_at': self.assigned_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }