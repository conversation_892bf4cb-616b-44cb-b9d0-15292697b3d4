#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 订单管理API路由
版本: 1.0
创建时间: 2025-01-15

处理订单相关的所有API请求，包括创建、查询、更新状态等操作
"""

from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import and_, or_, desc

from models import db, Order, OrderItem, OrderStatusHistory, OrderApproval, Material, User, InventoryItem, Notification
from services.order_service import OrderService
from utils.decorators import validate_json, require_active_user
from utils.response import (
    success_response, error_response, paginated_response, created_response,
    not_found_response, updated_response
)
from utils.validators import paginate_query, generate_order_number, is_valid_enum

# 创建蓝图
orders_bp = Blueprint('orders', __name__)

@orders_bp.route('/', methods=['GET'])
@jwt_required()
def get_orders():
    """
    获取订单列表接口
    功能描述：获取当前用户的订单列表
    入参：{ status: string, priority: string, page: int, size: int }
    返回参数：分页的订单列表数据
    url地址：/api/v1/orders
    请求方式：GET
    """
    try:
        current_user_id = int(get_jwt_identity())  # 转换为整数
        
        # 验证分页参数
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        
        if page < 1:
            return jsonify({
                'error': 400,
                'message': '页码必须大于0',
                'body': {},
                'success': False
            }), 400
        
        if size < 1 or size > 100:
            return jsonify({
                'error': 400,
                'message': '每页数量必须在1-100之间',
                'body': {},
                'success': False
            }), 400
        
        # 获取查询参数
        status = request.args.get('status', '').strip()
        priority = request.args.get('priority', '').strip()
        
        # 构建查询 - 只返回用户作为买方或供应商的订单
        query = Order.query.filter(
            or_(
                Order.buyer_id == current_user_id,
                Order.supplier_id == current_user_id
            )
        )
        
        # 状态筛选
        if status and is_valid_enum(status, ['pending', 'confirmed', 'processing', 'shipping', 'completed', 'cancelled']):
            query = query.filter(Order.status == status)
        
        # 优先级筛选
        if priority and is_valid_enum(priority, ['aog', 'high', 'normal', 'low']):
            query = query.filter(Order.priority == priority)
        
        # 按创建时间降序排列
        query = query.order_by(desc(Order.created_at))
        
        # 分页查询
        pagination, pagination_info = paginate_query(query, page, size)
        
        # 获取订单详情
        orders_data = []
        for order in pagination.items:
            order_dict = order.to_dict()
            
            # 添加用户关系信息
            order_dict['is_buyer'] = order.buyer_id == current_user_id
            order_dict['is_supplier'] = order.supplier_id == current_user_id
            
            orders_data.append(order_dict)
        
        return paginated_response(
            orders_data,
            pagination_info,
            '订单列表获取成功'
        )
        
    except Exception as e:
        return error_response(500, f'获取订单列表失败: {str(e)}')

@orders_bp.route('/', methods=['POST'])
@jwt_required()
def create_order():
    """
    创建订单接口
    功能描述：创建新的订单
    入参：{ supplier_id: int, items: array, delivery_address: string, notes: string }
    返回参数：创建的订单信息
    url地址：/api/v1/orders
    请求方式：POST
    """
    try:
        current_user_id = int(get_jwt_identity())  # 转换为整数
        data = request.get_json()

        # 验证必填字段
        required_fields = ['supplier_id', 'items', 'delivery_address']
        for field in required_fields:
            if not data.get(field):
                return error_response(400, f'缺少必填字段: {field}')

        # 验证订单项目
        items = data['items']
        if not isinstance(items, list) or len(items) == 0:
            return error_response(400, '订单项目不能为空')

        # 使用服务层创建订单
        order, message = OrderService.create_order(current_user_id, data)

        return created_response(order.to_dict(include_history=True, include_approvals=True), message)

    except ValueError as e:
        return error_response(400, str(e))
    except Exception as e:
        return error_response(500, f'创建订单失败: {str(e)}')

@orders_bp.route('/<int:order_id>', methods=['GET'])
@jwt_required()
def get_order(order_id):
    """
    获取订单详情接口
    功能描述：获取指定订单的详细信息
    入参：{ order_id: int }
    返回参数：订单详细信息
    url地址：/api/v1/orders/{order_id}
    请求方式：GET
    """
    try:
        current_user_id = int(get_jwt_identity())  # 转换为整数

        order = Order.query.get(order_id)
        if not order:
            return not_found_response('订单')
        
        # 验证访问权限
        if order.buyer_id != current_user_id and order.supplier_id != current_user_id:
            return error_response(403, '无权访问此订单')
        
        order_dict = order.to_dict()
        
        # 添加用户关系信息
        order_dict['is_buyer'] = order.buyer_id == current_user_id
        order_dict['is_supplier'] = order.supplier_id == current_user_id
        
        # 添加买方和供应商信息
        if order.buyer:
            order_dict['buyer_info'] = {
                'id': order.buyer.id,
                'company_name': order.buyer.company_name,
                'real_name': order.buyer.real_name,
                'phone': order.buyer.phone
            }
        
        if order.supplier:
            order_dict['supplier_info'] = {
                'id': order.supplier.id,
                'company_name': order.supplier.company_name,
                'real_name': order.supplier.real_name,
                'phone': order.supplier.phone
            }
        
        return success_response(order_dict, '订单详情获取成功')
        
    except Exception as e:
        return error_response(500, f'获取订单详情失败: {str(e)}')



@orders_bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_order_statistics():
    """
    获取订单统计信息接口
    功能描述：获取当前用户的订单统计数据
    入参：无
    返回参数：统计信息
    url地址：/api/v1/orders/statistics
    请求方式：GET
    """
    try:
        current_user_id = int(get_jwt_identity())  # 转换为整数

        # 基础统计
        total_orders = Order.query.filter(
            or_(
                Order.buyer_id == current_user_id,
                Order.supplier_id == current_user_id
            )
        ).count()
        
        # 按状态统计
        status_stats = db.session.query(
            Order.status,
            db.func.count(Order.id).label('count')
        ).filter(
            or_(
                Order.buyer_id == current_user_id,
                Order.supplier_id == current_user_id
            )
        ).group_by(Order.status).all()
        
        # 按优先级统计
        priority_stats = db.session.query(
            Order.priority,
            db.func.count(Order.id).label('count')
        ).filter(
            or_(
                Order.buyer_id == current_user_id,
                Order.supplier_id == current_user_id
            )
        ).group_by(Order.priority).all()
        
        # 总金额统计
        total_amount = db.session.query(
            db.func.sum(Order.total_amount)
        ).filter(
            or_(
                Order.buyer_id == current_user_id,
                Order.supplier_id == current_user_id
            ),
            Order.status != 'cancelled'
        ).scalar() or 0
        
        # 近期订单趋势（最近30天）
        start_date = datetime.utcnow() - timedelta(days=30)
        recent_orders = db.session.query(
            db.func.date(Order.created_at).label('date'),
            db.func.count(Order.id).label('count')
        ).filter(
            or_(
                Order.buyer_id == current_user_id,
                Order.supplier_id == current_user_id
            ),
            Order.created_at >= start_date
        ).group_by(db.func.date(Order.created_at)).order_by(db.func.date(Order.created_at)).all()
        
        statistics = {
            'total_orders': total_orders,
            'total_amount': float(total_amount),
            'status_distribution': [
                {'status': stat.status, 'count': stat.count}
                for stat in status_stats
            ],
            'priority_distribution': [
                {'priority': stat.priority, 'count': stat.count}
                for stat in priority_stats
            ],
            'recent_trend': [
                {'date': str(trend.date), 'count': trend.count}
                for trend in recent_orders
            ]
        }
        
        return success_response(statistics, '订单统计信息获取成功')
        
    except Exception as e:
        return error_response(500, f'获取订单统计信息失败: {str(e)}')

@orders_bp.route('/<int:order_id>/items', methods=['GET'])
@jwt_required()
def get_order_items(order_id):
    """
    获取订单项目接口
    功能描述：获取指定订单的项目列表
    入参：{ order_id: int }
    返回参数：订单项目列表
    url地址：/api/v1/orders/{order_id}/items
    请求方式：GET
    """
    try:
        current_user_id = int(get_jwt_identity())  # 转换为整数

        order = Order.query.get(order_id)
        if not order:
            return not_found_response('订单')
        
        # 验证访问权限
        if order.buyer_id != current_user_id and order.supplier_id != current_user_id:
            return error_response(403, '无权访问此订单')
        
        # 获取订单项目
        items = OrderItem.query.filter_by(order_id=order_id).all()
        items_data = [item.to_dict() for item in items]
        
        return success_response(items_data, '订单项目获取成功')
        
    except Exception as e:
        return error_response(500, f'获取订单项目失败: {str(e)}')

@orders_bp.route('/<int:order_id>/status', methods=['PUT'])
@jwt_required()
def update_order_status(order_id):
    """
    更新订单状态接口
    功能描述：更新订单状态
    入参：{ status: string, reason: string, notes: string }
    返回参数：更新结果
    url地址：/api/v1/orders/{order_id}/status
    请求方式：PUT
    """
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        # 验证必填字段
        if not data.get('status'):
            return error_response(400, '缺少必填字段: status')

        # 使用服务层更新状态
        success, message = OrderService.update_order_status(
            order_id=order_id,
            new_status=data['status'],
            operator_id=current_user_id,
            reason=data.get('reason'),
            notes=data.get('notes')
        )

        if success:
            # 获取更新后的订单
            order = Order.query.get(order_id)
            return updated_response(order.to_dict(include_history=True), message)
        else:
            return error_response(400, message)

    except Exception as e:
        return error_response(500, f'更新订单状态失败: {str(e)}')

@orders_bp.route('/<int:order_id>/approve', methods=['POST'])
@jwt_required()
def approve_order(order_id):
    """
    审批订单接口
    功能描述：审批订单
    入参：{ action: string, approval_level: int, comments: string }
    返回参数：审批结果
    url地址：/api/v1/orders/{order_id}/approve
    请求方式：POST
    """
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()

        # 验证必填字段
        required_fields = ['action', 'approval_level']
        for field in required_fields:
            if not data.get(field):
                return error_response(400, f'缺少必填字段: {field}')

        if data['action'] not in ['approve', 'reject']:
            return error_response(400, 'action必须是approve或reject')

        # 使用服务层处理审批
        success, message = OrderService.approve_order(
            order_id=order_id,
            approver_id=current_user_id,
            approval_level=data['approval_level'],
            action=data['action'],
            comments=data.get('comments')
        )

        if success:
            # 获取更新后的订单
            order = Order.query.get(order_id)
            return updated_response(order.to_dict(include_approvals=True), message)
        else:
            return error_response(400, message)

    except Exception as e:
        return error_response(500, f'审批订单失败: {str(e)}')

@orders_bp.route('/<int:order_id>/history', methods=['GET'])
@jwt_required()
def get_order_history(order_id):
    """
    获取订单状态历史接口
    功能描述：获取订单的状态变更历史
    入参：无
    返回参数：状态历史列表
    url地址：/api/v1/orders/{order_id}/history
    请求方式：GET
    """
    try:
        current_user_id = int(get_jwt_identity())

        # 验证订单存在
        order = Order.query.get(order_id)
        if not order:
            return not_found_response('订单不存在')

        # 检查权限（买方或供应商可以查看）
        if order.buyer_id != current_user_id and order.supplier_id != current_user_id:
            return error_response(403, '无权限查看此订单')

        # 获取状态历史
        history = OrderStatusHistory.query.filter_by(order_id=order_id)\
            .order_by(OrderStatusHistory.created_at.desc()).all()

        history_data = [h.to_dict() for h in history]

        return success_response(history_data, '获取订单历史成功')

    except Exception as e:
        return error_response(500, f'获取订单历史失败: {str(e)}')

@orders_bp.route('/pending', methods=['GET'])
@jwt_required()
def get_pending_orders():
    """
    获取待处理订单接口
    功能描述：获取当前用户需要处理的订单
    入参：无
    返回参数：待处理订单列表
    url地址：/api/v1/orders/pending
    请求方式：GET
    """
    try:
        current_user_id = int(get_jwt_identity())  # 转换为整数

        # 获取待处理订单
        pending_orders = Order.query.filter(
            or_(
                # 作为供应商的待确认订单
                and_(
                    Order.supplier_id == current_user_id,
                    Order.status == 'pending'
                ),
                # 作为买方的待收货订单
                and_(
                    Order.buyer_id == current_user_id,
                    Order.status == 'shipping'
                )
            )
        ).order_by(desc(Order.created_at)).all()
        
        orders_data = []
        for order in pending_orders:
            order_dict = order.to_dict()
            order_dict['is_buyer'] = order.buyer_id == current_user_id
            order_dict['is_supplier'] = order.supplier_id == current_user_id
            orders_data.append(order_dict)
        
        return success_response(orders_data, '待处理订单获取成功')
        
    except Exception as e:
        return error_response(500, f'获取待处理订单失败: {str(e)}')